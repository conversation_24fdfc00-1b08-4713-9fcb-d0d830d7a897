import { useRef } from "react";

// Import logos for better path handling in Vite
import logo1 from "@/assets/images/fictional-company-logo-1.png";
import logo2 from "@/assets/images/fictional-company-logo-2.png";
import logo3 from "@/assets/images/fictional-company-logo-3.png";
import logo4 from "@/assets/images/fictional-company-logo-4.png";
import logo5 from "@/assets/images/fictional-company-logo-5.png";

interface CompanyLogo {
  id: string;
  name: string;
  logo: string;
  url?: string;
}

// Company logos data using real assets
const mockLogos: CompanyLogo[] = [
  { id: "1", name: "Company 1", logo: logo1 },
  { id: "2", name: "Company 2", logo: logo2 },
  { id: "3", name: "Company 3", logo: logo3 },
  { id: "4", name: "Company 4", logo: logo4 },
  { id: "5", name: "Company 5", logo: logo5 },
  { id: "6", name: "Company 1", logo: logo1 },
  { id: "7", name: "Company 2", logo: logo2 },
  { id: "8", name: "Company 3", logo: logo3 },
];

interface LogosCarouselProps {
  scrollSpeed?: number; // pixels per second
}

export function LogosCarousel({ scrollSpeed = 80 }: LogosCarouselProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 根据scrollSpeed计算动画持续时间
  // 估算每个logo的平均宽度（包括padding和gap）约为 160px
  const averageLogoWidth = 160;
  const totalWidth = mockLogos.length * averageLogoWidth;
  const animationDuration = totalWidth / scrollSpeed; // 秒

  // 控制动画暂停/播放
  const handleMouseEnter = () => {
    const container = scrollContainerRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(".logos-scroll-animation");
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "paused";
    });
  };

  const handleMouseLeave = () => {
    const container = scrollContainerRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(".logos-scroll-animation");
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "running";
    });
  };

  return (
    <section className="relative py-12 overflow-hidden">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-6 md:mb-8">
          <div className="flex items-center justify-center gap-2 mb-4 px-4">
            <span className="text-xl md:text-2xl">🔥</span>
            <h2 className="text-lg md:text-xl lg:text-2xl font-semibold text-white text-center">
              The best performance marketing teams are using Miuta
            </h2>
            <span className="text-xl md:text-2xl">🔥</span>
          </div>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Logos Container */}
          <div
            ref={scrollContainerRef}
            className="w-full overflow-hidden relative"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            {/* 使用CSS动画的滚动容器 */}
            <div
              className="flex gap-4 md:gap-8 logos-scroll-animation items-center"
              style={{
                animationDuration: `${animationDuration}s`,
              }}
            >
              {/* 双倍 dom 用于无缝循环 */}
              {[...mockLogos, ...mockLogos].map((logo, index) => (
                <div
                  key={`${logo.id}-${index}`}
                  className="flex-shrink-0 flex items-center justify-center"
                >
                  <div className="w-24 h-10 md:w-32 md:h-12 flex items-center justify-center rounded-lg cursor-pointer logo-hover">
                    <img
                      src={logo.logo}
                      alt={logo.name}
                      className="max-w-full max-h-full object-contain opacity-60 hover:opacity-80 transition-opacity"
                      onError={(e) => {
                        // Fallback to text if image fails to load
                        e.currentTarget.style.display = "none";
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          parent.innerHTML = `<span class="text-white/60 text-sm font-medium">${logo.name}</span>`;
                        }
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
