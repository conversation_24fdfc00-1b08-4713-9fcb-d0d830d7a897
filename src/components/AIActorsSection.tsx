import { useState } from "react";

interface ActorVideo {
  id: string;
  name: string;
  videoUrl: string;
  thumbnail: string;
  position: {
    row: number;
    col: number;
    height: number;
  };
}

// Mock actor videos data
const mockActorVideos: ActorVideo[] = [
  {
    id: "1",
    name: "<PERSON>",
    videoUrl: "/videos/full/video-1.mp4",
    thumbnail: "/videos/thumbnails/video-1.png",
    position: { row: 1, col: 1, height: 300 },
  },
  {
    id: "2",
    name: "<PERSON>",
    videoUrl: "/videos/full/video-1.mp4",
    thumbnail: "/videos/thumbnails/video-1.png",
    position: { row: 1, col: 2, height: 250 },
  },
  {
    id: "3",
    name: "<PERSON>",
    videoUrl: "/videos/full/video-1.mp4",
    thumbnail: "/videos/thumbnails/video-1.png",
    position: { row: 1, col: 3, height: 350 },
  },
  {
    id: "4",
    name: "<PERSON>",
    videoUrl: "/videos/full/video-1.mp4",
    thumbnail: "/videos/thumbnails/video-1.png",
    position: { row: 1, col: 4, height: 280 },
  },
  {
    id: "5",
    name: "<PERSON>",
    videoUrl: "/videos/full/video-1.mp4",
    thumbnail: "/videos/thumbnails/video-1.png",
    position: { row: 2, col: 1, height: 320 },
  },
  {
    id: "6",
    name: "Alex",
    videoUrl: "/videos/full/video-1.mp4",
    thumbnail: "/videos/thumbnails/video-1.png",
    position: { row: 2, col: 2, height: 290 },
  },
  {
    id: "7",
    name: "Maya",
    videoUrl: "/videos/full/video-1.mp4",
    thumbnail: "/videos/thumbnails/video-1.png",
    position: { row: 2, col: 3, height: 340 },
  },
  {
    id: "8",
    name: "Jake",
    videoUrl: "/videos/full/video-1.mp4",
    thumbnail: "/videos/thumbnails/video-1.png",
    position: { row: 2, col: 4, height: 310 },
  },
];

const AIActorsSection = () => {
  const [hoveredVideo, setHoveredVideo] = useState<string | null>(null);

  return (
    <section className="w-full py-16 md:py-24">
      <div className="mx-auto px-8">
        {/* Section Header */}
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-[#E5D2FD] mb-6 md:mb-8">
            Meet our{" "}
            <span
              style={{
                background:
                  "linear-gradient(180deg, #D2B1FD 0%, #C293FD 36.06%, #B076FB 72.6%, #7550D6 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              AI Actors
            </span>
          </h2>
          <p className="text-base md:text-lg lg:text-xl text-[#B6A0D3] max-w-4xl mx-auto leading-normal font-normal">
            Use Miuta's AI actor library as your AI UGC creator to create the perfect UGC style ads
            campaign that resonate with your target audience and drive results.
          </p>
        </div>

        {/* Video Masonry Grid */}
        <div className="relative max-w-6xl mx-auto mb-12 md:mb-16">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
            {mockActorVideos.map((actor) => (
              <div
                key={actor.id}
                className="relative rounded-2xl overflow-hidden cursor-pointer transition-transform duration-300 hover:scale-105"
                style={{
                  height: `${actor.position.height}px`,
                  gridRow: `span ${Math.ceil(actor.position.height / 100)}`,
                }}
                onMouseEnter={() => setHoveredVideo(actor.id)}
                onMouseLeave={() => setHoveredVideo(null)}
              >
                {/* Video Element */}
                <video
                  src={actor.videoUrl}
                  className={`w-full h-full object-cover transition-opacity duration-300 ${
                    hoveredVideo === actor.id ? 'opacity-100' : 'opacity-0'
                  }`}
                  muted
                  loop
                  playsInline
                  ref={(video) => {
                    if (video) {
                      if (hoveredVideo === actor.id) {
                        video.play().catch(() => {});
                      } else {
                        video.pause();
                        video.currentTime = 0;
                      }
                    }
                  }}
                />

                {/* Thumbnail */}
                <div
                  className={`absolute inset-0 transition-opacity duration-300 ${
                    hoveredVideo === actor.id ? 'opacity-0' : 'opacity-100'
                  }`}
                >
                  <img
                    src={actor.thumbnail}
                    alt={actor.name}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Play Button Overlay */}
                <div
                  className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${
                    hoveredVideo === actor.id ? 'opacity-0' : 'opacity-100'
                  }`}
                >
                  <div className="w-16 h-16 bg-black/60 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <div className="w-0 h-0 border-l-[12px] border-l-white border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Text */}
        <div className="text-center mb-8">
          <h3 className="text-2xl md:text-3xl lg:text-4xl font-extrabold text-white">
            and 200+ more
          </h3>
        </div>

        {/* CTA Button */}
        <div className="flex justify-center">
          <button
            className="group bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-purple-500/25 hover:scale-105"
            style={{
              background:
                "linear-gradient(90deg, rgba(117, 66, 247, 0) 31.7%, rgba(117, 66, 247, 0.2) 76.03%), linear-gradient(270deg, rgba(0, 0, 0, 0) 51.68%, rgba(7, 0, 16, 0.2) 100%), linear-gradient(90deg, rgba(102, 6, 224, 0) 81.78%, #4e03ac 100%), linear-gradient(180deg, #771ee9 1.09%, #7425e3 100%)",
              filter:
                "drop-shadow(-5px -5px 50px rgba(127, 77, 248, 0.9)) drop-shadow(10px 20px 40px rgba(74, 103, 249, 0.2))",
              boxShadow: "inset 0 0 0 0.5px rgba(252, 252, 252, 0.1)",
            }}
          >
            Get Started
            <span className="group-hover:translate-x-1 transition-transform duration-300">
              →
            </span>
          </button>
        </div>
      </div>
    </section>
  );
};

export { AIActorsSection };