import { useState, useRef } from "react";

interface VideoItem {
  id: string;
  thumbnail: string;
  videoUrl?: string;
}

interface VideoState {
  [key: string]: {
    isLoading: boolean;
    hasError: boolean;
    canPlay: boolean;
    hasPlayedOnce: boolean; // 记录是否已经播放过一次
  };
}

// Mock video data - in a real app, this would come from an API
const mockVideos: VideoItem[] = [
  {
    id: "1",
    thumbnail: "/videos/thumbnails/video-1.png",
    videoUrl: "/videos/full/video-1.mp4",
  },
  {
    id: "2",
    thumbnail: "https://picsum.photos/300/400?random=2",
    videoUrl: "/videos/full/video-1.mp4",
  },
  {
    id: "3",
    thumbnail: "https://picsum.photos/300/400?random=3",
    videoUrl: "/videos/full/video-1.mp4",
  },
  {
    id: "4",
    thumbnail: "https://picsum.photos/300/400?random=4",
    videoUrl: "/videos/full/video-1.mp4",
  },
  {
    id: "5",
    thumbnail: "/api/placeholder/300/400",
    videoUrl: "/videos/full/video-1.mp4",
  },
  {
    id: "6",
    thumbnail: "/api/placeholder/300/400",
    videoUrl: "/videos/full/video-1.mp4",
  },
  {
    id: "7",
    thumbnail: "/api/placeholder/300/400",
    videoUrl: "/videos/full/video-1.mp4",
  },
  {
    id: "8",
    thumbnail: "/api/placeholder/300/400",
    videoUrl: "/videos/full/video-1.mp4",
  },
];

interface VideoCarouselProps {
  scrollSpeed?: number; // pixels per second
}

export function VideoCarousel({ scrollSpeed = 200 }: VideoCarouselProps) {
  const [videoStates, setVideoStates] = useState<VideoState>({});
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 根据scrollSpeed计算动画持续时间
  // scrollSpeed是像素/秒，我们需要计算滚动完整个宽度需要多少秒
  // 假设每个视频卡片宽度是216px + 12px gap = 228px，8个视频 = 1824px
  const totalWidth = mockVideos.length * 228; // 228px per video (216px + 12px gap)
  const animationDuration = totalWidth / scrollSpeed; // 秒

  // 处理视频加载开始
  const handleVideoLoadStart = (videoId: string) => {
    setVideoStates((prev) => ({
      ...prev,
      [videoId]: {
        ...prev[videoId], // 保留之前的状态（特别是 hasPlayedOnce）
        isLoading: true,
        hasError: false,
        canPlay: false,
      },
    }));
  };

  // 处理视频可以播放
  const handleVideoCanPlay = (videoId: string) => {
    setVideoStates((prev) => ({
      ...prev,
      [videoId]: {
        ...prev[videoId],
        isLoading: false,
        canPlay: true,
      },
    }));
  };

  // 处理视频开始播放
  const handleVideoPlay = (videoId: string) => {
    setVideoStates((prev) => ({
      ...prev,
      [videoId]: {
        ...prev[videoId],
        hasPlayedOnce: true, // 标记为已播放过
      },
    }));
  };

  // 处理视频加载错误
  const handleVideoError = (videoId: string) => {
    setVideoStates((prev) => ({
      ...prev,
      [videoId]: {
        ...prev[videoId],
        isLoading: false,
        hasError: true,
        canPlay: false,
      },
    }));
  };

  // 获取视频状态
  const getVideoState = (videoId: string) => {
    return (
      videoStates[videoId] || {
        isLoading: true,
        hasError: false,
        canPlay: false,
        hasPlayedOnce: false,
      }
    );
  };

  // 控制动画暂停/播放
  const handleMouseEnter = () => {
    const container = scrollContainerRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(
      ".video-scroll-animation"
    );
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "paused";
    });
  };

  const handleMouseLeave = () => {
    const container = scrollContainerRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(
      ".video-scroll-animation"
    );
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "running";
    });
  };

  return (
    <section className="relative overflow-hidden">
      <div className="container mx-auto px-6">
        {/* Carousel Container */}
        <div className="relative">
          {/* Video Grid */}
          <div
            ref={scrollContainerRef}
            className="w-full overflow-hidden relative"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            style={{
              transform: "translate3d(0,0,0)", // 提前开启硬件加速
            }}
          >
            {/* 使用CSS动画的滚动容器 */}
            <div
              className="flex gap-3 video-scroll-animation"
              style={{
                animationDuration: `${animationDuration}s`,
                willChange: "transform", // 告诉浏览器提前做好优化准备
              }}
            >
              {/* 双倍 dom 用于无缝循环 */}
              {[...mockVideos, ...mockVideos].map((video, index) => {
                const videoKey = `${video.id}-${index}`;
                const videoState = getVideoState(videoKey);
                // 只有在视频从未播放过，且（加载中或失败或无法播放）时才显示缩略图
                const shouldShowThumbnail =
                  !videoState.hasPlayedOnce &&
                  (videoState.isLoading ||
                    videoState.hasError ||
                    !videoState.canPlay);
                return (
                  <div
                    key={videoKey}
                    className="flex-shrink-0 carousel-fade-in"
                    style={{
                      width: "216px",
                      height: "384px",
                      animationDelay: `${index * 100}ms`,
                    }}
                  >
                    <div
                      className="relative rounded-xl overflow-hidden bg-gradient-card border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-card h-full w-full"
                      style={{
                        transform: "translate3d(0, 0, 0)", // 提前开启硬件加速
                        backfaceVisibility: "hidden", // 防止闪烁
                        willChange: "transform", // 告诉浏览器提前优化
                        WebkitFontSmoothing: "antialiased", // 防止字体抖动
                      }}
                    >
                      {/* Video Thumbnail - 显示在视频加载中或失败时 */}
                      <div
                        className={`absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 z-10 transition-opacity duration-300 ${
                          shouldShowThumbnail ? "opacity-100" : "opacity-0"
                        }`}
                        style={{ aspectRatio: "9/16" }}
                      >
                        <img
                          src={video.thumbnail}
                          alt="video thumbnail"
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            // Fallback to gradient background if image fails to load
                            e.currentTarget.style.display = "none";
                          }}
                        />
                      </div>

                      {/* Video Player */}
                      {video.videoUrl && (
                        <video
                          src={video.videoUrl}
                          className={`w-full h-full object-cover transition-opacity duration-300 ${
                            shouldShowThumbnail ? "opacity-0" : "opacity-100"
                          }`}
                          autoPlay
                          muted
                          loop
                          playsInline
                          onLoadStart={() => handleVideoLoadStart(videoKey)}
                          onCanPlay={() => handleVideoCanPlay(videoKey)}
                          onPlay={() => handleVideoPlay(videoKey)}
                          onError={() => handleVideoError(videoKey)}
                        />
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
