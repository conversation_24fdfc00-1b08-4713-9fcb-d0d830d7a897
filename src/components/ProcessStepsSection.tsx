const ProcessStepsSection = () => {
  return (
    <section className="w-full py-16 md:py-24">
      <div className="mx-auto px-8">
        {/* Section Header */}
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-[#E5D2FD] mb-6 md:mb-8">
            Create AI UGC videos{" "}
            <span
              style={{
                background:
                  "linear-gradient(180deg, #D2B1FD 0%, #C293FD 36.06%, #B076FB 72.6%, #7550D6 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              in minutes
            </span>{" "}
          </h2>
          <p className="text-base md:text-lg lg:text-xl text-[#B6A0D3] max-w-4xl mx-auto leading-normal font-normal">
            Generate scroll-stopping content with AI, anytime you want
          </p>
        </div>

        {/* Process Steps */}
        <div className="flex flex-col lg:flex-row gap-6 md:gap-8 mb-12 md:mb-16">
          {/* Step 1 */}
          <div
            className="flex-1 flex flex-col items-center border border-purple-500/30 rounded-xl px-6 py-8 backdrop-blur-sm"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            {/* Step Image Placeholder */}
            <div className="w-full h-48 md:h-56 mb-6 rounded-lg overflow-hidden bg-gradient-to-br from-purple-900/30 to-indigo-900/30 flex items-center justify-center">
              <div className="w-full h-full bg-black/20 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="text-sm text-white/60">Script Interface</div>
                </div>
              </div>
            </div>

            {/* Step Label */}
            <div className="mb-4">
              <span className="inline-block px-3 py-1 text-xs font-semibold text-purple-300 bg-purple-600/20 border border-purple-500/30 rounded-full">
                STEP 1
              </span>
            </div>

            {/* Step Title */}
            <h3 className="text-xl md:text-2xl font-bold text-white mb-4 text-center">
              Write Your Script
            </h3>

            {/* Step Description */}
            <p className="text-sm md:text-base text-white/80 text-center leading-relaxed">
              Enter or automatically generate a script that aligns with your
              brand message to personalize your AI-generated content.
            </p>
          </div>

          {/* Step 2 */}
          <div
            className="flex-1 flex flex-col items-center border border-purple-500/30 rounded-xl px-6 py-8 backdrop-blur-sm"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            {/* Step Image Placeholder */}
            <div className="w-full h-48 md:h-56 mb-6 rounded-lg overflow-hidden bg-gradient-to-br from-purple-900/30 to-indigo-900/30 flex items-center justify-center">
              <div className="w-full h-full bg-black/20 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="text-sm text-white/60">Avatar Selection</div>
                </div>
              </div>
            </div>

            {/* Step Label */}
            <div className="mb-4">
              <span className="inline-block px-3 py-1 text-xs font-semibold text-purple-300 bg-purple-600/20 border border-purple-500/30 rounded-full">
                STEP 2
              </span>
            </div>

            {/* Step Title */}
            <h3 className="text-xl md:text-2xl font-bold text-white mb-4 text-center">
              Pick An Avatar
            </h3>

            {/* Step Description */}
            <p className="text-sm md:text-base text-white/80 text-center leading-relaxed">
              Select from over 100 unique AI avatars to represent your brand's
              style and give a visual identity to your video.
            </p>
          </div>

          {/* Step 3 */}
          <div
            className="flex-1 flex flex-col items-center border border-purple-500/30 rounded-xl px-6 py-8 backdrop-blur-sm"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            {/* Step Image Placeholder */}
            <div className="w-full h-48 md:h-56 mb-6 rounded-lg overflow-hidden bg-gradient-to-br from-purple-900/30 to-indigo-900/30 flex items-center justify-center">
              <div className="w-full h-full bg-black/20 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="text-sm text-white/60">Video Output</div>
                </div>
              </div>
            </div>

            {/* Step Label */}
            <div className="mb-4">
              <span className="inline-block px-3 py-1 text-xs font-semibold text-purple-300 bg-purple-600/20 border border-purple-500/30 rounded-full">
                STEP 3
              </span>
            </div>

            {/* Step Title */}
            <h3 className="text-xl md:text-2xl font-bold text-white mb-4 text-center">
              Generate Your Video
            </h3>

            {/* Step Description */}
            <p className="text-sm md:text-base text-white/80 text-center leading-relaxed">
              Combine the selected avatar and script to quickly produce a
              high-quality, personalized video for your brand.
            </p>
          </div>
        </div>

        {/* CTA Button */}
        <div className="flex justify-center">
          <button
            className="group bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-purple-500/25 hover:scale-105"
            style={{
              background:
                "linear-gradient(90deg, rgba(117, 66, 247, 0) 31.7%, rgba(117, 66, 247, 0.2) 76.03%), linear-gradient(270deg, rgba(0, 0, 0, 0) 51.68%, rgba(7, 0, 16, 0.2) 100%), linear-gradient(90deg, rgba(102, 6, 224, 0) 81.78%, #4e03ac 100%), linear-gradient(180deg, #771ee9 1.09%, #7425e3 100%)",
              filter:
                "drop-shadow(-5px -5px 50px rgba(127, 77, 248, 0.9)) drop-shadow(10px 20px 40px rgba(74, 103, 249, 0.2))",
              boxShadow: "inset 0 0 0 0.5px rgba(252, 252, 252, 0.1)",
            }}
          >
            Get Started
            <span className="group-hover:translate-x-1 transition-transform duration-300">
              →
            </span>
          </button>
        </div>
      </div>
    </section>
  );
};

export { ProcessStepsSection };