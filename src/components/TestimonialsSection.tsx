import { useRef } from "react";

interface Testimonial {
  id: string;
  rating: number;
  text: string;
  author: {
    name: string;
    title: string;
    company: string;
    avatar: string;
  };
}

// Mock testimonials data
const mockTestimonials: Testimonial[] = [
  {
    id: "1",
    rating: 5,
    text: "<PERSON><PERSON>'s AI-driven editing is a game-changer. It produces professional and polished videos in no time, allowing us to focus on other important tasks.",
    author: {
      name: "<PERSON>",
      title: "Digital Marketer",
      company: "Large Corporation",
      avatar: "https://picsum.photos/40/40?random=1",
    },
  },
  {
    id: "2",
    rating: 5,
    text: "<PERSON><PERSON>'s AI-driven editing is a game-changer. It produces professional and polished videos in no time, allowing us to focus on other important tasks.",
    author: {
      name: "<PERSON>",
      title: "Digital Marketer",
      company: "Large Corporation",
      avatar: "https://picsum.photos/40/40?random=2",
    },
  },
  {
    id: "3",
    rating: 5,
    text: "<PERSON><PERSON>'s AI-driven editing is a game-changer. It produces professional and polished videos in no time, allowing us to focus on other important tasks.",
    author: {
      name: "<PERSON>",
      title: "Digital Marketer",
      company: "Large Corporation",
      avatar: "https://picsum.photos/40/40?random=3",
    },
  },
  {
    id: "4",
    rating: 5,
    text: "Miuta's AI-driven editing is a game-changer. It produces professional and polished videos in no time, allowing us to focus on other important tasks.",
    author: {
      name: "Olivia Brown",
      title: "Digital Marketer",
      company: "Large Corporation",
      avatar: "https://picsum.photos/40/40?random=4",
    },
  },
  {
    id: "5",
    rating: 2,
    text: "Miuta's AI-driven editing is a game-changer. It produces professional and polished videos in no time, allowing us to focus on other important tasks.",
    author: {
      name: "Olivia Brown",
      title: "Digital Marketer",
      company: "Large Corporation",
      avatar: "https://picsum.photos/40/40?random=5",
    },
  },
  {
    id: "6",
    rating: 5,
    text: "Miuta's AI-driven editing is a game-changer. It produces professional and polished videos in no time, allowing us to focus on other important tasks.",
    author: {
      name: "Olivia Brown",
      title: "Digital Marketer",
      company: "Large Corporation",
      avatar: "https://picsum.photos/40/40?random=6",
    },
  },
];

interface TestimonialsSectionProps {
  scrollSpeed?: number; // pixels per second
}

const TestimonialsSection = ({ scrollSpeed = 60 }: TestimonialsSectionProps) => {
  const firstRowRef = useRef<HTMLDivElement>(null);
  const secondRowRef = useRef<HTMLDivElement>(null);

  // 根据scrollSpeed计算动画持续时间
  const averageCardWidth = 320; // 估算每个卡片的平均宽度
  const totalWidth = mockTestimonials.length * averageCardWidth;
  const animationDuration = totalWidth / scrollSpeed; // 秒

  // 控制第一行动画暂停/播放
  const handleFirstRowMouseEnter = () => {
    const container = firstRowRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(".testimonials-scroll-left");
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "paused";
    });
  };

  const handleFirstRowMouseLeave = () => {
    const container = firstRowRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(".testimonials-scroll-left");
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "running";
    });
  };

  // 控制第二行动画暂停/播放
  const handleSecondRowMouseEnter = () => {
    const container = secondRowRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(".testimonials-scroll-right");
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "paused";
    });
  };

  const handleSecondRowMouseLeave = () => {
    const container = secondRowRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(".testimonials-scroll-right");
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "running";
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span key={index} className={`text-lg ${index < rating ? 'text-yellow-400' : 'text-gray-500'}`}>
        ★
      </span>
    ));
  };

  const TestimonialCard = ({ testimonial }: { testimonial: Testimonial }) => (
    <div
      className="flex-shrink-0 w-80 p-6 rounded-xl border border-purple-500/30 backdrop-blur-sm"
      style={{
        background: "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
      }}
    >
      {/* Rating Stars */}
      <div className="flex gap-1 mb-4">
        {renderStars(testimonial.rating)}
      </div>

      {/* Testimonial Text */}
      <p className="text-white text-sm md:text-base leading-relaxed mb-6">
        "{testimonial.text}"
      </p>

      {/* Separator Line */}
      <div className="w-full h-px bg-white/20 mb-4"></div>

      {/* Author Info */}
      <div className="flex items-center gap-3">
        <img
          src={testimonial.author.avatar}
          alt={testimonial.author.name}
          className="w-10 h-10 rounded-full object-cover"
        />
        <div>
          <h4 className="text-white font-medium text-sm">{testimonial.author.name}</h4>
          <p className="text-white/60 text-xs">{testimonial.author.title}</p>
          <p className="text-white/60 text-xs">{testimonial.author.company}</p>
        </div>
      </div>
    </div>
  );

  return (
    <section className="w-full py-16 md:py-24">
      <div className="mx-auto px-8">
        {/* Section Header */}
        <div className="text-center mb-12 md:mb-16">
          {/* Wall of Love Badge */}
          <div className="inline-block mb-6">
            <span className="px-4 py-2 bg-purple-600/20 border border-purple-500/30 rounded-full text-white text-sm font-medium">
              The Wall of Love
            </span>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-[#E5D2FD] mb-6 md:mb-8">
            Join{" "}
            <span
              style={{
                background:
                  "linear-gradient(180deg, #D2B1FD 0%, #C293FD 36.06%, #B076FB 72.6%, #7550D6 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              70,000+ users
            </span>{" "}
            waiting for Miuta
          </h2>
          <p className="text-base md:text-lg lg:text-xl text-[#B6A0D3] max-w-4xl mx-auto leading-normal font-normal">
            We are grateful for all the love Miuta has received so far. We really appreciate the support.
          </p>
        </div>

        {/* First Row - Scroll Left */}
        <div className="mb-8">
          <div
            ref={firstRowRef}
            className="w-full overflow-hidden relative"
            onMouseEnter={handleFirstRowMouseEnter}
            onMouseLeave={handleFirstRowMouseLeave}
          >
            <div
              className="flex gap-6 testimonials-scroll-left"
              style={{
                animationDuration: `${animationDuration}s`,
              }}
            >
              {/* 双倍 dom 用于无缝循环 */}
              {[...mockTestimonials, ...mockTestimonials].map((testimonial, index) => (
                <TestimonialCard key={`first-${testimonial.id}-${index}`} testimonial={testimonial} />
              ))}
            </div>
          </div>
        </div>

        {/* Second Row - Scroll Right */}
        <div className="mb-12 md:mb-16">
          <div
            ref={secondRowRef}
            className="w-full overflow-hidden relative"
            onMouseEnter={handleSecondRowMouseEnter}
            onMouseLeave={handleSecondRowMouseLeave}
          >
            <div
              className="flex gap-6 testimonials-scroll-right"
              style={{
                animationDuration: `${animationDuration}s`,
              }}
            >
              {/* 双倍 dom 用于无缝循环 */}
              {[...mockTestimonials, ...mockTestimonials].map((testimonial, index) => (
                <TestimonialCard key={`second-${testimonial.id}-${index}`} testimonial={testimonial} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export { TestimonialsSection };