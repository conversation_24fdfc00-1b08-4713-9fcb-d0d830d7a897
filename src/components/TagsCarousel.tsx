import { useRef } from "react";

interface Tag {
  id: string;
  name: string;
  color?: string;
}

// Mock tags data based on the design mockup
const mockTags: Tag[] = [
  { id: "1", name: "Short Launches" },
  { id: "2", name: "Youtube Shorts" },
  { id: "3", name: "Instagram Reels" },
  { id: "4", name: "TikTok Videos" },
  { id: "5", name: "UGC Content" },
  { id: "6", name: "UGC Creators" },
  { id: "7", name: "AI Actors" },
  { id: "8", name: "Youtube Influencers" },
  { id: "9", name: "Amazon Influencers" },
  { id: "10", name: "TikTok Creators" },
  { id: "11", name: "Amazon Shoppable" },
  { id: "12", name: "TikTok Videos" },
  { id: "13", name: "UGC Creators" },
  { id: "14", name: "AI Actors" },
  { id: "15", name: "Youtube Influencers" },
  { id: "16", name: "Amazon Influencers" },
];

interface TagsCarouselProps {
  scrollSpeed?: number; // pixels per second
}

export function TagsCarousel({ scrollSpeed = 100 }: TagsCarouselProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 根据scrollSpeed计算动画持续时间
  // 估算每个标签的平均宽度（包括padding和gap）约为 120px
  const averageTagWidth = 120;
  const totalWidth = mockTags.length * averageTagWidth;
  const animationDuration = totalWidth / scrollSpeed; // 秒

  // 控制动画暂停/播放
  const handleMouseEnter = () => {
    const container = scrollContainerRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(".tags-scroll-animation");
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "paused";
    });
  };

  const handleMouseLeave = () => {
    const container = scrollContainerRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(".tags-scroll-animation");
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "running";
    });
  };

  return (
    <section className="relative py-8 overflow-hidden">
      <div className="container mx-auto px-6">
        {/* Carousel Container */}
        <div className="relative">
          {/* Tags Container */}
          <div
            ref={scrollContainerRef}
            className="w-full overflow-hidden relative"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            {/* 使用CSS动画的滚动容器 */}
            <div
              className="flex gap-3 tags-scroll-animation"
              style={{
                animationDuration: `${animationDuration}s`,
              }}
            >
              {/* 双倍 dom 用于无缝循环 */}
              {[...mockTags, ...mockTags].map((tag, index) => (
                <div key={`${tag.id}-${index}`} className="flex-shrink-0">
                  <span className="inline-block px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm font-medium text-white/80 bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 rounded-full cursor-pointer tag-hover hover:text-white whitespace-nowrap">
                    {tag.name}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
