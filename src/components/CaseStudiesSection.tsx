import { useRef } from "react";

interface CaseStudy {
  id: string;
  user: {
    name: string;
    title: string;
    company: string;
    avatar: string;
  };
  roas: string;
  description: string;
  favoriteFeatures: string[];
  images: string[];
}

// Mock case studies data
const mockCaseStudies: CaseStudy[] = [
  {
    id: "1",
    user: {
      name: "<PERSON>",
      title: "User acquisition at Faceplay",
      company: "Faceplay",
      avatar: "https://picsum.photos/40/40?random=11",
    },
    roas: "120%",
    description: "We got a ROAS of 120%, giving us an unbeatable ROI on ad spend!",
    favoriteFeatures: [
      "The custom AI avatars gave our in-app campaigns a human feel. It's like we had real people demoing Faceplay — but better.",
      "The videos felt native to TikTok and Reels. They weren't just scroll-stoppers — users actually thought they were real people on and engaged like crazy."
    ],
    images: [
      "https://picsum.photos/200/300?random=21",
      "https://picsum.photos/200/300?random=22"
    ],
  },
  {
    id: "2",
    user: {
      name: "<PERSON>",
      title: "User acquisition at Faceplay",
      company: "Faceplay",
      avatar: "https://picsum.photos/40/40?random=12",
    },
    roas: "150%",
    description: "We got a ROAS of 150%, giving us an unbeatable ROI on ad spend!",
    favoriteFeatures: [
      "The custom AI avatars gave our in-app campaigns a human feel. It's like we had real people demoing Faceplay — but better.",
      "The videos felt native to TikTok and Reels. They weren't just scroll-stoppers — users actually thought they were real people on and engaged like crazy."
    ],
    images: [
      "https://picsum.photos/200/300?random=23",
      "https://picsum.photos/200/300?random=24"
    ],
  },
  {
    id: "3",
    user: {
      name: "Ronnie",
      title: "User acquisition at Faceplay",
      company: "Faceplay",
      avatar: "https://picsum.photos/40/40?random=13",
    },
    roas: "180%",
    description: "We got a ROAS of 180%, giving us an unbeatable ROI on ad spend!",
    favoriteFeatures: [
      "The custom AI avatars gave our in-app campaigns a human feel. It's like we had real people demoing Faceplay — but better.",
      "The videos felt native to TikTok and Reels. They weren't just scroll-stoppers — users actually thought they were real people on and engaged like crazy."
    ],
    images: [
      "https://picsum.photos/200/300?random=25",
      "https://picsum.photos/200/300?random=26"
    ],
  },
  {
    id: "4",
    user: {
      name: "Ronnie",
      title: "User acquisition at Faceplay",
      company: "Faceplay",
      avatar: "https://picsum.photos/40/40?random=14",
    },
    roas: "200%",
    description: "We got a ROAS of 200%, giving us an unbeatable ROI on ad spend!",
    favoriteFeatures: [
      "The custom AI avatars gave our in-app campaigns a human feel. It's like we had real people demoing Faceplay — but better.",
      "The videos felt native to TikTok and Reels. They weren't just scroll-stoppers — users actually thought they were real people on and engaged like crazy."
    ],
    images: [
      "https://picsum.photos/200/300?random=27",
      "https://picsum.photos/200/300?random=28"
    ],
  },
];

interface CaseStudiesSectionProps {
  scrollSpeed?: number; // pixels per second
}

const CaseStudiesSection = ({ scrollSpeed = 50 }: CaseStudiesSectionProps) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 根据scrollSpeed计算动画持续时间
  const averageCardWidth = 600; // 估算每个卡片的平均宽度
  const totalWidth = mockCaseStudies.length * averageCardWidth;
  const animationDuration = totalWidth / scrollSpeed; // 秒

  // 控制动画暂停/播放
  const handleMouseEnter = () => {
    const container = scrollContainerRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(".case-studies-scroll-left");
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "paused";
    });
  };

  const handleMouseLeave = () => {
    const container = scrollContainerRef.current;
    if (!container) return;
    const animatedElements = container.querySelectorAll(".case-studies-scroll-left");
    animatedElements.forEach((el: any) => {
      el.style.animationPlayState = "running";
    });
  };

  const CaseStudyCard = ({ caseStudy }: { caseStudy: CaseStudy }) => (
    <div
      className="flex-shrink-0 w-[580px] p-6 rounded-xl border border-purple-500/30 backdrop-blur-sm"
      style={{
        background: "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
      }}
    >
      <div className="flex gap-6">
        {/* Left side - Content */}
        <div className="flex-1">
          {/* User Info */}
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 rounded-full overflow-hidden bg-purple-600/30 flex items-center justify-center">
              <span className="text-sm">💼</span>
            </div>
            <img
              src={caseStudy.user.avatar}
              alt={caseStudy.user.name}
              className="w-8 h-8 rounded-full object-cover"
            />
            <div>
              <h4 className="text-white font-medium text-sm">{caseStudy.user.name}</h4>
              <p className="text-white/60 text-xs">{caseStudy.user.title}</p>
            </div>
          </div>

          {/* ROAS Description */}
          <p className="text-white text-lg font-semibold mb-6">
            {caseStudy.description}
          </p>

          {/* Favorite Features */}
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-3">
              <span className="text-purple-400">✨</span>
              <span className="text-white font-medium text-sm">Favorite Features</span>
            </div>
            
            <div className="space-y-3">
              {caseStudy.favoriteFeatures.map((feature, index) => (
                <div
                  key={index}
                  className="p-3 bg-white/5 border border-white/10 rounded-lg"
                >
                  <p className="text-white/80 text-xs leading-relaxed">
                    {feature}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right side - Single Image */}
        <div className="flex-shrink-0">
          <div className="w-64 h-80 rounded-lg overflow-hidden bg-gradient-to-br from-purple-900/30 to-indigo-900/30">
            {caseStudy.images[0] ? (
              <img
                src={caseStudy.images[0]}
                alt="Case study preview"
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-purple-600/30 rounded-lg flex items-center justify-center">
                    <span className="text-2xl">📊</span>
                  </div>
                  <div className="text-sm text-white/60">Case Study</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <section className="w-full py-16 md:py-24">
      <div className="mx-auto px-8">
        {/* Section Header */}
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-[#E5D2FD] mb-6 md:mb-8">
            And we've got{" "}
            <span
              style={{
                background:
                  "linear-gradient(180deg, #D2B1FD 0%, #C293FD 36.06%, #B076FB 72.6%, #7550D6 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              case studies
            </span>{" "}
            to back it up...
          </h2>
        </div>

        {/* Case Studies Carousel */}
        <div className="mb-12 md:mb-16">
          <div
            ref={scrollContainerRef}
            className="w-full overflow-hidden relative"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <div
              className="flex gap-6 case-studies-scroll-left"
              style={{
                animationDuration: `${animationDuration}s`,
              }}
            >
              {/* 双倍 dom 用于无缝循环 */}
              {[...mockCaseStudies, ...mockCaseStudies].map((caseStudy, index) => (
                <CaseStudyCard key={`${caseStudy.id}-${index}`} caseStudy={caseStudy} />
              ))}
            </div>
          </div>
        </div>

        {/* CTA Button */}
        <div className="flex justify-center">
          <button
            className="group bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-purple-500/25 hover:scale-105"
            style={{
              background:
                "linear-gradient(90deg, rgba(117, 66, 247, 0) 31.7%, rgba(117, 66, 247, 0.2) 76.03%), linear-gradient(270deg, rgba(0, 0, 0, 0) 51.68%, rgba(7, 0, 16, 0.2) 100%), linear-gradient(90deg, rgba(102, 6, 224, 0) 81.78%, #4e03ac 100%), linear-gradient(180deg, #771ee9 1.09%, #7425e3 100%)",
              filter:
                "drop-shadow(-5px -5px 50px rgba(127, 77, 248, 0.9)) drop-shadow(10px 20px 40px rgba(74, 103, 249, 0.2))",
              boxShadow: "inset 0 0 0 0.5px rgba(252, 252, 252, 0.1)",
            }}
          >
            Create now
            <span className="group-hover:translate-x-1 transition-transform duration-300">
              →
            </span>
          </button>
        </div>
      </div>
    </section>
  );
};

export { CaseStudiesSection };