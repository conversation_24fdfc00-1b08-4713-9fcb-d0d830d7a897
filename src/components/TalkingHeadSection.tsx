const TalkingHeadSection = () => {
  return (
    <section className="w-full py-16 md:py-24">
      <div className="mx-auto px-8">
        <div className="flex flex-col lg:flex-row items-center gap-12 md:gap-16">
          {/* Left Side - Image Preview Grid */}
          <div className="flex-1 flex justify-center lg:justify-start">
            <div
              className="relative p-6 rounded-2xl border border-purple-500/30"
              style={{
                background:
                  "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
              }}
            >
              <div className="grid grid-cols-3 gap-4">
                {/* Three preview images */}
                {[1, 2, 3].map((index) => (
                  <div
                    key={index}
                    className="w-24 h-32 md:w-28 md:h-36 rounded-xl overflow-hidden bg-gradient-to-br from-purple-900/30 to-indigo-900/30 flex items-center justify-center"
                  >
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-2 bg-purple-600/30 rounded-lg flex items-center justify-center">
                        <span className="text-xl">👤</span>
                      </div>
                      <div className="text-xs text-white/60">Avatar {index}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Side - Content */}
          <div className="flex-1 text-center lg:text-left">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-[#E5D2FD] mb-6 md:mb-8">
              <span
                style={{
                  background:
                    "linear-gradient(180deg, #D2B1FD 0%, #C293FD 36.06%, #B076FB 72.6%, #7550D6 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                }}
              >
                Talking Head Video
              </span>
            </h2>
            <p className="text-base md:text-lg lg:text-xl text-[#B6A0D3] leading-normal font-normal max-w-2xl">
              Our AI Actors add emotion and stick to scripts, totally changing how you create interactive UGC content.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export { TalkingHeadSection };