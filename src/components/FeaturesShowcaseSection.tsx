const FeaturesShowcaseSection = () => {
  const languages = [
    { code: "de", name: "<PERSON><PERSON><PERSON>", flag: "🇩🇪" },
    { code: "es", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", flag: "🇪🇸" },
    { code: "fr", name: "Français", flag: "🇫🇷" },
    { code: "id", name: "Indonesian", flag: "🇮🇩" },
    { code: "it", name: "Italia", flag: "🇮🇹" },
    { code: "pt", name: "Português", flag: "🇧🇷" },
  ];

  return (
    <section className="w-full py-16 md:py-24">
      <div className="mx-auto px-8">
        {/* Different Scenes Section */}
        <div className="flex flex-col lg:flex-row items-center gap-12 md:gap-16 mb-20 md:mb-32">
          {/* Left Side - Content */}
          <div className="flex-1 text-center lg:text-left">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-[#E5D2FD] mb-6 md:mb-8">
              Over{" "}
              <span
                style={{
                  background:
                    "linear-gradient(180deg, #D2B1FD 0%, #C293FD 36.06%, #B076FB 72.6%, #7550D6 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                }}
              >
                20+ Different
              </span>{" "}
              Scenes
            </h2>
            <p className="text-base md:text-lg lg:text-xl text-[#B6A0D3] leading-normal font-normal max-w-2xl">
              From walking in the park to podcast videos we have a multitude of background and scenes to match your content style
            </p>
          </div>

          {/* Right Side - Scene Preview Grid */}
          <div className="flex-1 flex justify-center lg:justify-end">
            <div
              className="relative p-6 rounded-2xl border border-purple-500/30"
              style={{
                background:
                  "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
              }}
            >
              <div className="grid grid-cols-3 gap-4">
                {[1, 2, 3].map((index) => (
                  <div
                    key={index}
                    className="w-24 h-32 md:w-28 md:h-36 rounded-xl overflow-hidden bg-gradient-to-br from-purple-900/30 to-indigo-900/30 flex items-center justify-center"
                  >
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-2 bg-purple-600/30 rounded-lg flex items-center justify-center">
                        <span className="text-xl">🎬</span>
                      </div>
                      <div className="text-xs text-white/60">Scene {index}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* AI Voiceover Section */}
        <div className="flex flex-col lg:flex-row items-center gap-12 md:gap-16 mb-20 md:mb-32">
          {/* Left Side - Language Grid */}
          <div className="flex-1 flex justify-center lg:justify-start">
            <div
              className="relative p-6 rounded-2xl border border-purple-500/30"
              style={{
                background:
                  "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
              }}
            >
              <div className="grid grid-cols-2 gap-3">
                {languages.map((lang) => (
                  <div
                    key={lang.code}
                    className="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg cursor-pointer transition-all duration-300"
                  >
                    <span className="text-lg">{lang.flag}</span>
                    <span className="text-sm text-white font-medium">{lang.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Side - Content */}
          <div className="flex-1 text-center lg:text-left">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-[#E5D2FD] mb-6 md:mb-8">
              AI Voiceover: Access to{" "}
              <span
                style={{
                  background:
                    "linear-gradient(180deg, #D2B1FD 0%, #C293FD 36.06%, #B076FB 72.6%, #7550D6 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                }}
              >
                29+ Languages
              </span>
            </h2>
            <p className="text-base md:text-lg lg:text-xl text-[#B6A0D3] leading-normal font-normal max-w-2xl">
              With our AI voiceover, you can produce authentic videos in over 29 languages. Our lifelike voices will help you create professional, engaging content that connects with viewers worldwide.
            </p>
          </div>
        </div>

        {/* Built for Marketing Teams Section */}
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-[#E5D2FD] mb-6 md:mb-8">
            Built for{" "}
            <span
              style={{
                background:
                  "linear-gradient(180deg, #D2B1FD 0%, #C293FD 36.06%, #B076FB 72.6%, #7550D6 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              marketing teams
            </span>
          </h2>
        </div>

        {/* Marketing Teams Cards */}
        <div className="flex flex-col lg:flex-row gap-6 md:gap-8 mb-12 md:mb-16">
          {/* ECommerce Brands */}
          <div
            className="flex-1 flex flex-col border border-purple-500/30 rounded-xl px-6 py-8 backdrop-blur-sm"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            {/* Image Placeholder */}
            <div className="w-full h-48 md:h-56 mb-6 rounded-lg overflow-hidden bg-gradient-to-br from-purple-900/30 to-indigo-900/30 flex items-center justify-center">
              <div className="w-full h-full bg-black/20 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-purple-600/30 rounded-lg flex items-center justify-center">
                    <span className="text-2xl">🛒</span>
                  </div>
                  <div className="text-sm text-white/60">ECommerce</div>
                </div>
              </div>
            </div>

            <h3 className="text-xl md:text-2xl font-bold text-white mb-4 text-center">
              ECommerce Brands
            </h3>
            <p className="text-sm md:text-base text-white/80 text-center leading-relaxed">
              Skip costly style shoots – generate high-quality AI video clips instantly.
            </p>
          </div>

          {/* Performance Marketers */}
          <div
            className="flex-1 flex flex-col border border-purple-500/30 rounded-xl px-6 py-8 backdrop-blur-sm"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            {/* Image Placeholder */}
            <div className="w-full h-48 md:h-56 mb-6 rounded-lg overflow-hidden bg-gradient-to-br from-purple-900/30 to-indigo-900/30 flex items-center justify-center">
              <div className="w-full h-full bg-black/20 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-purple-600/30 rounded-lg flex items-center justify-center">
                    <span className="text-2xl">📊</span>
                  </div>
                  <div className="text-sm text-white/60">Performance</div>
                </div>
              </div>
            </div>

            <h3 className="text-xl md:text-2xl font-bold text-white mb-4 text-center">
              Performance Marketers
            </h3>
            <p className="text-sm md:text-base text-white/80 text-center leading-relaxed">
              Launch 100+ creatives a day effortlessly and A/B test to maximize ROAS at speed.
            </p>
          </div>

          {/* Business Owners */}
          <div
            className="flex-1 flex flex-col border border-purple-500/30 rounded-xl px-6 py-8 backdrop-blur-sm"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            {/* Image Placeholder */}
            <div className="w-full h-48 md:h-56 mb-6 rounded-lg overflow-hidden bg-gradient-to-br from-purple-900/30 to-indigo-900/30 flex items-center justify-center">
              <div className="w-full h-full bg-black/20 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-purple-600/30 rounded-lg flex items-center justify-center">
                    <span className="text-2xl">💼</span>
                  </div>
                  <div className="text-sm text-white/60">Business</div>
                </div>
              </div>
            </div>

            <h3 className="text-xl md:text-2xl font-bold text-white mb-4 text-center">
              Business Owners
            </h3>
            <p className="text-sm md:text-base text-white/80 text-center leading-relaxed">
              Create ad-ready AI videos in minutes. Scale your video ads without scaling your team.
            </p>
          </div>
        </div>

        {/* CTA Button */}
        <div className="flex justify-center">
          <button
            className="group bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-purple-500/25 hover:scale-105"
            style={{
              background:
                "linear-gradient(90deg, rgba(117, 66, 247, 0) 31.7%, rgba(117, 66, 247, 0.2) 76.03%), linear-gradient(270deg, rgba(0, 0, 0, 0) 51.68%, rgba(7, 0, 16, 0.2) 100%), linear-gradient(90deg, rgba(102, 6, 224, 0) 81.78%, #4e03ac 100%), linear-gradient(180deg, #771ee9 1.09%, #7425e3 100%)",
              filter:
                "drop-shadow(-5px -5px 50px rgba(127, 77, 248, 0.9)) drop-shadow(10px 20px 40px rgba(74, 103, 249, 0.2))",
              boxShadow: "inset 0 0 0 0.5px rgba(252, 252, 252, 0.1)",
            }}
          >
            Create now
            <span className="group-hover:translate-x-1 transition-transform duration-300">
              →
            </span>
          </button>
        </div>
      </div>
    </section>
  );
};

export { FeaturesShowcaseSection };