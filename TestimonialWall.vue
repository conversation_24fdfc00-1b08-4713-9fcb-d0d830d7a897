<template>
  <div class="relative flex flex-col gap-8 md:gap-40">
    <div v-if="isMobile" class="relative overflow-x-hidden">
      <div class="flex" ref="mobileContainer" :style="{ transform: `translateX(${mobilePosition}px)` }">
        <TestimonialCard
          v-for="(testimonial, index) in [...allTestimonials, ...allTestimonials, ...allTestimonials]"
          :key="`mobile-${index}`"
          :avatar="testimonial.avatar as ImageId"
          :name="testimonial.name"
          :title="testimonial.title"
          :content="testimonial.content"
          class="mx-3 inline-block"
        />
      </div>
    </div>
    <template v-else>
      <div class="relative overflow-x-hidden">
        <div class="flex" ref="topContainer" :style="{ transform: `translateX(${topPosition}px)` }">
          <TestimonialCard
            v-for="(testimonial, index) in [...topTestimonials, ...topTestimonials, ...topTestimonials]"
            :key="`top-${index}`"
            :avatar="testimonial.avatar as ImageId"
            :name="testimonial.name"
            :title="testimonial.title"
            :content="testimonial.content"
            class="mx-3 inline-block"
          />
        </div>
      </div>
      <div class="relative overflow-x-hidden">
        <div class="flex" ref="bottomContainer" :style="{ transform: `translateX(${bottomPosition}px)` }">
          <TestimonialCard
            v-for="(testimonial, index) in [...bottomTestimonials, ...bottomTestimonials, ...bottomTestimonials]"
            :key="`bottom-${index}`"
            :avatar="testimonial.avatar as ImageId"
            :name="testimonial.name"
            :title="testimonial.title"
            :content="testimonial.content"
            class="mx-3 inline-block"
          />
        </div>
      </div>
    </template>
    <div
      class="inset-0 top-1/2 mx-auto flex h-fit w-fit max-w-[czalc(100vw-48px)] flex-col items-center justify-center gap-8 bg-[radial-gradient(66.34%_66.34%_at_50%_50%,rgba(255,255,255,0.9)_66.81%,rgba(255,255,255,0)_100%)] md:absolute md:-translate-y-1/2 md:px-12 md:py-8"
    >
      <LogoSquare class="size-[70px]" />
      <h2 class="max-w-[calc(100vw-32px)] text-center text-[28px] font-bold leading-[34px] text-gray-900 md:max-w-[707px] md:text-[34px] md:leading-10">
        {{ tt('static_pages.home_page.join_4m_learners_boosting_productivity_10x_smarter_not_harder') }}
      </h2>
      <CommonButtonPrimary
        tag="a"
        :href="localePath('/signin')"
        class="!rounded-[10px] !border-none !px-[53.5px] !py-3 text-xl font-semibold leading-7 md:!rounded-lg"
        >{{ tt('static_pages.header.sign_up') }}</CommonButtonPrimary
      >
    </div>
  </div>
</template>
<script setup lang="ts">
import type { ImageId } from '#build/image-src'
import LogoSquare from '@/assets/icons/logo-square.svg'
import TestimonialCard from './TestimonialCard.vue'
const { isMobile } = useIsMobile()
const { tt } = useExtendedI18n()
const localePath = useLocalePath()

const allTestimonials = [
  {
    avatar: 'homepage-user-01',
    name: 'ブレイクスルー佐々木',
    title: '@BreakthroughSSK',
    content:
      'Mapifyとは本当に、どんなものでもマインドマップ化できる神サイトなのですが、やりは超簡単！\nマインドマップ化したい対象の項目を選んで、リンクを貼り付けてMapifyのボタンを押すだけ、YouTube動画も一瞬でマインドマップ化できてしまいました。\nせっかくYouTubeで解説動画を観たのに翌日にはすべて忘れてしまうという人は情報を頭の中で整理できていないことが多いです。\nしかしMapifyを使うと瞬時に頭の中を階層構造で整理することができるので、勉強効率・記憶効率がマジで10倍になります。',
  },
  {
    avatar: 'homepage-user-04',
    name: 'The Angry Explainer',
    title: '@theangryexplainer',
    content:
      "It's a game-changer if you're trying to study multiple subjects FASTER. One of its best features is the ability to turn PDFs or texts into mind maps. I used to drown in endless pages of text, wasting time trying to dig out the important stuff. Now, with Mapify, I instantly see the main points of each document. This saved me so much time and made EVERYTHING way easier to understand!",
  },
  {
    avatar: 'homepage-user-02',
    name: 'Akiyama Yuta',
    title: '@AkiyamaYuta',
    content:
      '仕事だったり勉強していると、たくさんの情報に圧倒されたり整理する時間が足りなくて困ったりします。\nMapifyを使えばそんな悩みを解決することができます。このツールのすごいところとしては\nPDFだったり、動画、ウェブサイトなどのいろんな形式の情報を自動的にマインドマップにしてくれるということです。\n実際に使ってみたところAIが整理してくれるので、今まで手間がかかっていた作業があっという間に終わることができました。',
  },
  {
    avatar: 'homepage-user-07',
    name: '親愛的路 Dear Road｜SoNia',
    title: '@DearRoad',
    content:
      '比起從零發想，它能提供豐富的靈感，甚至考量到一些你沒想到的元素跟作法！而且你可以在這個根基上，再去做刪減跟編輯，又或是點右鍵，請它在生成更多點子，畫到你滿意為止。',
  },
  {
    avatar: 'homepage-user-08',
    name: 'adam.godigital',
    title: '@AdamDigital',
    content:
      "Your business reports due tomorrow, and you have no time writing reports? Your boss wants you to quickly organize complex info into flowcharts for a presentation for your colleagues? It's super easy! With one click presentation mode, preparing impactful pitches has never been easier.",
  },
  {
    avatar: 'homepage-user-06',
    name: 'Daniel | Tech & Data',
    title: '@Daniel-Dann',
    content:
      "Mapify's blend of AI smarts and online searching is a match made in heaven, helping you uncover hidden connections and gain some serious wisdom along the way. Plus, it ensures your content stays accurate and true to your vision. It's all about streamlining your workflow and boosting productivity – who doesn't love that?",
  },
  {
    avatar: 'homepage-user-03',
    name: 'リモートワーク研究所【リモ研】',
    title: '@remote-work',
    content:
      'この1年で数千以上の「生成AIサービス」がリリースされて、様々なものをトライしてきましたが、この1年で最もヒットした生成AIツールの1つが「Mapify」\nマインドマップ×生成AIというシンプルなコンセプトながら、情報整理＆理解・アイデア出し・資料作成の3つのシーンにおいて、欠かせないツールの一角となっています。',
  },
  {
    avatar: 'homepage-user-05',
    name: '哈佛姐夢遊矽谷 AliceInSiliconWonderland',
    title: '@aliceinsilliconwonderland',
    content:
      '如果你是考生的話，用 Mapify 你只要上傳任何歷史、國學常識的 PDF 檔，只要幾秒鐘之後 Mapify 就可以幫你整理出好幾十頁的講義精華。我覺得這對於學習來說真的是非常有效率。',
  },
]
const halfLength = Math.ceil(allTestimonials.length / 2)
const topTestimonials = allTestimonials.slice(0, halfLength)
const bottomTestimonials = allTestimonials.slice(halfLength)

const topContainer = ref<HTMLElement | null>(null)
const bottomContainer = ref<HTMLElement | null>(null)
const mobileContainer = ref<HTMLElement | null>(null)

const topPosition = ref(0)
const bottomPosition = ref(0)
const mobilePosition = ref(0)

const topSpeed = 0.5
const bottomSpeed = 0.4
const mobileSpeed = 0.45

const { pause: pauseTopAnimation, resume: resumeTopAnimation } = useRafFn(
  () => {
    if (isMobile.value || !topContainer.value) return
    const singleSetWidth = topContainer.value.scrollWidth / 3
    if (singleSetWidth <= 0) return

    topPosition.value += topSpeed
    if (topPosition.value >= singleSetWidth) {
      topPosition.value -= singleSetWidth
    }
  },
  { immediate: false },
)

const { pause: pauseBottomAnimation, resume: resumeBottomAnimation } = useRafFn(
  () => {
    if (isMobile.value || !bottomContainer.value) return
    const singleSetWidth = bottomContainer.value.scrollWidth / 3
    if (singleSetWidth <= 0) return

    bottomPosition.value -= bottomSpeed
    if (-bottomPosition.value >= singleSetWidth) {
      bottomPosition.value += singleSetWidth
    }
  },
  { immediate: false },
)

const { pause: pauseMobileAnimation, resume: resumeMobileAnimation } = useRafFn(
  () => {
    if (!isMobile.value || !mobileContainer.value) return
    const singleSetWidth = mobileContainer.value.scrollWidth / 3
    if (singleSetWidth <= 0) return

    mobilePosition.value -= mobileSpeed // Or += depending on desired direction
    if (-mobilePosition.value >= singleSetWidth) {
      mobilePosition.value += singleSetWidth
    }
  },
  { immediate: false },
)

onMounted(() => {
  nextTick(() => {
    if (isMobile.value) {
      if (mobileContainer.value) {
        useResizeObserver(mobileContainer, () => {
          if (mobileContainer.value) {
            const mobileSingleSetWidth = mobileContainer.value.scrollWidth / 3
            if (mobileSingleSetWidth > 0) {
              mobilePosition.value = Math.max(-mobileSingleSetWidth, Math.min(0, mobilePosition.value))
            }
          }
        })
        resumeMobileAnimation()
      }
    } else {
      if (topContainer.value) {
        useResizeObserver(topContainer, () => {
          if (topContainer.value) {
            const topSingleSetWidth = topContainer.value.scrollWidth / 3
            if (topSingleSetWidth > 0) {
              topPosition.value = Math.max(0, Math.min(topSingleSetWidth, topPosition.value))
            }
          }
        })
        resumeTopAnimation()
      }

      if (bottomContainer.value) {
        useResizeObserver(bottomContainer, () => {
          if (bottomContainer.value) {
            const bottomSingleSetWidth = bottomContainer.value.scrollWidth / 3
            if (bottomSingleSetWidth > 0) {
              bottomPosition.value = Math.max(-bottomSingleSetWidth, Math.min(0, bottomPosition.value))
            }
          }
        })
        resumeBottomAnimation()
      }
    }
  })
})

onBeforeUnmount(() => {
  if (isMobile.value) {
    pauseMobileAnimation()
  } else {
    pauseTopAnimation()
    pauseBottomAnimation()
  }
})

watch(isMobile, newIsMobile => {
  if (newIsMobile) {
    pauseTopAnimation()
    pauseBottomAnimation()
    nextTick(() => {
      if (mobileContainer.value) {
        useResizeObserver(mobileContainer, () => {
          if (mobileContainer.value) {
            const mobileSingleSetWidth = mobileContainer.value.scrollWidth / 3
            if (mobileSingleSetWidth > 0) {
              mobilePosition.value = Math.max(-mobileSingleSetWidth, Math.min(0, mobilePosition.value))
            }
          }
        })
        resumeMobileAnimation()
      }
    })
  } else {
    pauseMobileAnimation()
    nextTick(() => {
      if (topContainer.value) {
        useResizeObserver(topContainer, () => {
          if (topContainer.value) {
            const topSingleSetWidth = topContainer.value.scrollWidth / 3
            if (topSingleSetWidth > 0) {
              topPosition.value = Math.max(0, Math.min(topSingleSetWidth, topPosition.value))
            }
          }
        })
        resumeTopAnimation()
      }
      if (bottomContainer.value) {
        useResizeObserver(bottomContainer, () => {
          if (bottomContainer.value) {
            const bottomSingleSetWidth = bottomContainer.value.scrollWidth / 3
            if (bottomSingleSetWidth > 0) {
              bottomPosition.value = Math.max(-bottomSingleSetWidth, Math.min(0, bottomPosition.value))
            }
          }
        })
        resumeBottomAnimation()
      }
    })
  }
})
</script>
